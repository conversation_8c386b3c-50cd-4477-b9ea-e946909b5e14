class AudioRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.audioBuffer = [];
        this.stream = null;
        this.isRecording = false;
        this.isSending = false;
        
        this.BUFFER_DURATION = 60000; // 60 seconds
        this.CHUNK_DURATION = 1000; // 1 second chunks
        this.MAX_CHUNKS = 60; // 60 chunks of 1 second each
        this.API_URL = 'http://localhost:8000/api/chat';
        
        // Add audio processing context for continuous buffer
        this.bufferAudioContext = null;
        this.bufferSource = null;
        this.bufferProcessor = null;
        this.rawAudioBuffer = []; // Store raw PCM data instead of encoded chunks
        this.sampleRate = 44100;

        // Silence detection properties
        this.silenceThreshold = 0.01; // Threshold for silence detection
        this.silenceTimeout = 2000; // 2 seconds of silence before stopping
        this.isListeningAfterWakeWord = false;
        this.silenceStartTime = null;
        this.wakeWordAudioBuffer = []; // Buffer for recording after wake word
        this.silenceCheckInterval = null;

        this.recordBtn = document.getElementById('recordBtn');
        this.wakeWordBtn = document.getElementById('wakeWordBtn');
        this.status = document.getElementById('status');
        this.userAudioDiv = document.getElementById('userAudio');
        this.userAudioPlayer = document.getElementById('userAudioPlayer');
        this.userTranscriptDiv = document.getElementById('userTranscript');
        this.aiResponseDiv = document.getElementById('aiResponse');
        this.aiAudioPlayer = document.getElementById('aiAudioPlayer');
        this.transcriptDiv = document.getElementById('transcript');
        this.wakeWordIndicator = document.getElementById('wakeWordIndicator');
        this.wakeWordStatus = document.getElementById('wakeWordStatus');
        
        this.recordBtn.addEventListener('click', () => this.sendLastMinute());
        this.wakeWordBtn.addEventListener('click', () => this.toggleWakeWordListening());
        
        // Wake word detection properties
        this.websocket = null;
        this.wakeWordEnabled = false;
        this.audioContext = null;
        this.scriptProcessor = null;

        // Typing sound properties
        this.typingSound = null;
        this.isTypingSoundPlaying = false;
        
        this.checkBrowserSupport();
        this.startContinuousRecording();
        // Don't initialize wake word detection automatically - wait for user interaction
    }
    
    checkBrowserSupport() {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            this.showError('Browser not supported. Please use a modern browser.');
            this.recordBtn.disabled = true;
            return false;
        }
        return true;
    }
    
    async startContinuousRecording() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: this.sampleRate
                } 
            });
            
            // Initialize audio context for raw audio capture
            this.bufferAudioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.sampleRate
            });

            // Create source from microphone
            this.bufferSource = this.bufferAudioContext.createMediaStreamSource(this.stream);

            // Create script processor for raw audio capture
            const bufferSize = 4096; // Larger buffer for efficiency
            this.bufferProcessor = this.bufferAudioContext.createScriptProcessor(bufferSize, 1, 1);

            this.bufferProcessor.onaudioprocess = (event) => {
                if (this.isRecording) {
            const inputData = event.inputBuffer.getChannelData(0);
                    // Store copy of the audio data
                    this.addToRawBuffer(new Float32Array(inputData));
                }
            };
            
            // Connect the audio graph
            this.bufferSource.connect(this.bufferProcessor);
            this.bufferProcessor.connect(this.bufferAudioContext.destination);

            this.isRecording = true;
            
            this.recordBtn.textContent = 'Send Last 60 Seconds';
            this.recordBtn.disabled = false;
            this.status.textContent = 'Continuously recording... Click button to send last 60 seconds.';
        } catch (error) {
            console.error('Error starting continuous recording:', error);
            this.showError(`Error: ${error.message}`);
        }
    }

    addToRawBuffer(audioData) {
        // Add timestamp to chunk
        const timestampedChunk = {
            data: audioData,
            timestamp: Date.now()
        };
        
        this.rawAudioBuffer.push(timestampedChunk);

        // Also add to wake word buffer if we're listening after wake word
        if (this.isListeningAfterWakeWord) {
            this.wakeWordAudioBuffer.push(timestampedChunk);

            // Check for silence in the current audio chunk
            this.checkForSilence(audioData);
        }

        // Remove chunks older than 60 seconds
        const cutoffTime = Date.now() - this.BUFFER_DURATION;
        this.rawAudioBuffer = this.rawAudioBuffer.filter(chunk => chunk.timestamp > cutoffTime);

        // Calculate total samples and limit if necessary
        const totalSamples = this.rawAudioBuffer.reduce((sum, chunk) => sum + chunk.data.length, 0);
        const maxSamples = this.sampleRate * 60; // 60 seconds worth of samples

        if (totalSamples > maxSamples) {
            // Remove oldest chunks until we're under the limit
            while (this.rawAudioBuffer.length > 0 &&
                   this.rawAudioBuffer.reduce((sum, chunk) => sum + chunk.data.length, 0) > maxSamples) {
                this.rawAudioBuffer.shift();
            }
        }
    }
    
    checkForSilence(audioData) {
        // Calculate RMS (Root Mean Square) to detect silence
        let sum = 0;
        for (let i = 0; i < audioData.length; i++) {
            sum += audioData[i] * audioData[i];
        }
        const rms = Math.sqrt(sum / audioData.length);

        const currentTime = Date.now();

        if (rms < this.silenceThreshold) {
            // Audio is below silence threshold
            if (this.silenceStartTime === null) {
                this.silenceStartTime = currentTime;
                this.updateWakeWordStatus('Listening... (silence detected)');
            } else {
                // Check if we've been silent for the required duration
                const silenceDuration = currentTime - this.silenceStartTime;
                if (silenceDuration >= this.silenceTimeout) {
                    this.handleSilenceDetected();
                }
            }
        } else {
            // Audio detected, reset silence timer
            this.silenceStartTime = null;
            this.updateWakeWordStatus('Listening... (speaking detected)');
        }
    }
    
    handleSilenceDetected() {
        console.log('Silence detected for 1 second, processing audio...');

        // Stop listening for more audio
        this.isListeningAfterWakeWord = false;
        this.silenceStartTime = null;

        // Clear any existing silence check interval
        if (this.silenceCheckInterval) {
            clearInterval(this.silenceCheckInterval);
            this.silenceCheckInterval = null;
        }

        // Notify backend that we're no longer listening (disable execution keyword)
        this.updateBackendListeningState(false);

        this.updateWakeWordStatus('Processing your request...');

        // Process the accumulated wake word audio buffer
        this.processWakeWordAudio();
    }
    async processWakeWordAudio() {
        try {
            if (this.wakeWordAudioBuffer.length === 0) {
                console.log('No audio captured after wake word');
                this.updateWakeWordStatus('Listening for "Scarlett"...');
                return;
            }

            // Combine all wake word audio chunks into a single buffer
            const totalLength = this.wakeWordAudioBuffer.reduce((sum, chunk) => sum + chunk.data.length, 0);
            const combinedBuffer = new Float32Array(totalLength);

            let offset = 0;
            for (const chunk of this.wakeWordAudioBuffer) {
                combinedBuffer.set(chunk.data, offset);
                offset += chunk.data.length;
            }
            // Create WAV blob from the wake word audio
            const wavBlob = this.createWavFromRawData(combinedBuffer, this.sampleRate);

           
            // Process the recording
            this.hideResponses();
            await this.processRecording(wavBlob);

            // Clear the wake word audio buffer
            this.wakeWordAudioBuffer = [];
            this.rawAudioBuffer = [];

            // Return to listening for wake word
            this.updateWakeWordStatus('Listening for "Scarlett"...');

        } catch (error) {
            console.error('Error processing wake word audio:', error);
            this.showError(`Error processing audio: ${error.message}`);
            this.updateWakeWordStatus('Listening for "Scarlett"...');
            this.wakeWordAudioBuffer = [];
        }
    }

    async sendLastMinute() {
        if (this.isSending || this.rawAudioBuffer.length === 0) {
            return;
        }
        
        this.isSending = true;
        this.recordBtn.disabled = true;
        this.hideResponses();
        
        try {
            // Combine all raw audio chunks into a single buffer
            const totalLength = this.rawAudioBuffer.reduce((sum, chunk) => sum + chunk.data.length, 0);
            const combinedBuffer = new Float32Array(totalLength);

            let offset = 0;
            for (const chunk of this.rawAudioBuffer) {
                combinedBuffer.set(chunk.data, offset);
                offset += chunk.data.length;
            }

            // Create WAV blob directly from raw audio data
            const wavBlob = this.createWavFromRawData(combinedBuffer, this.sampleRate);

            await this.processRecording(wavBlob);

            // Clear the audio buffer
            this.rawAudioBuffer = [];

        } catch (error) {
            console.error('Error sending audio:', error);
            this.showError(`Error sending audio: ${error.message}`);
        } finally {
            this.isSending = false;
            this.recordBtn.disabled = false;
            }
    }

    createWavFromRawData(floatData, sampleRate) {
        const length = floatData.length;
        const buffer = new ArrayBuffer(44 + length * 2);
        const view = new DataView(buffer);

        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        // RIFF chunk descriptor
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * 2, true);
        writeString(8, 'WAVE');

        // fmt sub-chunk
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true); // Subchunk1Size
        view.setUint16(20, 1, true); // AudioFormat (PCM)
        view.setUint16(22, 1, true); // NumChannels
        view.setUint32(24, sampleRate, true); // SampleRate
        view.setUint32(28, sampleRate * 2, true); // ByteRate
        view.setUint16(32, 2, true); // BlockAlign
        view.setUint16(34, 16, true); // BitsPerSample

        // data sub-chunk
        writeString(36, 'data');
        view.setUint32(40, length * 2, true);

        // Convert float samples to 16-bit PCM
        let offset = 44;
        for (let i = 0; i < length; i++) {
            const sample = Math.max(-1, Math.min(1, floatData[i]));
            view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
            offset += 2;
        }

        return new Blob([buffer], { type: 'audio/wav' });
    }

    async processRecording(wavBlob) {
        try {
            const audioUrl = URL.createObjectURL(wavBlob);
            this.userAudioPlayer.src = audioUrl;
            this.userAudioDiv.style.display = 'block';

            this.status.innerHTML = 'Sending to AI...<span class="loading"></span>';

            // Start typing sound to indicate processing
            this.startTypingSound();

            await this.sendToAPI(wavBlob);

        } catch (error) {
            console.error('Error processing recording:', error);
            this.showError(`Error processing audio: ${error.message}`);
            // Stop typing sound on error
            this.stopTypingSound();
        }
    }
    
    async sendToAPI(wavBlob) {
        try {
            const formData = new FormData();
            formData.append('audio_file', wavBlob, 'recording.wav');
            
            const response = await fetch(this.API_URL, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`API error: ${response.statusText}`);
            }
            
            const data = await response.json();

            // Check if this is a silent response for TV control
            if (data.silent_response) {
                console.log('Received silent response for TV control operation');

                // Stop typing sound since we received a response
                this.stopTypingSound();

                // Display user transcript if available
                if (data.user_transcript) {
                    this.userTranscriptDiv.textContent = `"${data.user_transcript}"`;
                    this.userTranscriptDiv.style.display = 'block';
                }

                // Show a brief status message
                this.status.textContent = 'TV control completed. Still recording...';

                // If we're in wake word mode, return to listening state
                if (this.wakeWordEnabled) {
                    this.updateWakeWordStatus('Listening for "Scarlett"...');
                }

                return; // Exit early, don't play any audio
            }

            if (data.audio_data && data.transcript) {
                // Stop typing sound before playing response audio
                this.stopTypingSound();

                // Convert base64 PCM16 data to WAV format
                const audioBytes = atob(data.audio_data);
                const pcm16Array = new Int16Array(audioBytes.length / 2);

                // Convert bytes to 16-bit integers
                for (let i = 0; i < pcm16Array.length; i++) {
                    const byte1 = audioBytes.charCodeAt(i * 2);
                    const byte2 = audioBytes.charCodeAt(i * 2 + 1);
                    pcm16Array[i] = (byte2 << 8) | byte1; // Little-endian
                }

                // Create WAV file from PCM16 data
                const wavBlob = this.createWavFromPCM16(pcm16Array, 24000); // Realtime API uses 24kHz
                const audioUrl = URL.createObjectURL(wavBlob);

                this.aiAudioPlayer.src = audioUrl;
                this.transcriptDiv.textContent = `"${data.transcript}"`;
                this.aiResponseDiv.style.display = 'block';

                // Display user transcript if available
                if (data.user_transcript) {
                    this.userTranscriptDiv.textContent = `"${data.user_transcript}"`;
                    this.userTranscriptDiv.style.display = 'block';
                }

                this.status.textContent = 'Response received! You can play the audio below. Still recording...';

                this.aiAudioPlayer.play();
            }
            
        } catch (error) {
            console.error('Error sending to API:', error);
            this.showError(`Error communicating with server: ${error.message}`);
            // Stop typing sound on error
            this.stopTypingSound();
        }
    }
    
    getSupportedMimeType() {
        const types = [
            'audio/webm;codecs=opus',
            'audio/webm',
            'audio/ogg;codecs=opus',
            'audio/mp4'
        ];
        
        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }
        
        return 'audio/webm';
    }
    
    showError(message) {
        this.status.textContent = message;
        this.status.classList.add('error');
        setTimeout(() => {
            this.status.classList.remove('error');
        }, 5000);
    }

    // Typing sound management methods
    startTypingSound() {
        if (this.isTypingSoundPlaying) {
            return; // Already playing
        }

        try {
            // Create new audio element if it doesn't exist
            if (!this.typingSound) {
                this.typingSound = new Audio('Typing.wav');
                this.typingSound.loop = true;
                this.typingSound.volume = 0.3; // Set to moderate volume
            }

            this.typingSound.currentTime = 0; // Reset to beginning
            this.typingSound.play();
            this.isTypingSoundPlaying = true;

            console.log('Started typing sound');
        } catch (error) {
            console.error('Error starting typing sound:', error);
        }
    }

    stopTypingSound() {
        if (!this.isTypingSoundPlaying || !this.typingSound) {
            return; // Not playing or doesn't exist
        }

        try {
            this.typingSound.pause();
            this.typingSound.currentTime = 0; // Reset to beginning for next time
            this.isTypingSoundPlaying = false;

            console.log('Stopped typing sound');
        } catch (error) {
            console.error('Error stopping typing sound:', error);
        }
    }
    
    hideResponses() {
        this.userAudioDiv.style.display = 'none';
        this.aiResponseDiv.style.display = 'none';
        this.userTranscriptDiv.textContent = '';
        this.transcriptDiv.textContent = '';
        // Stop typing sound when hiding responses (e.g., starting new recording)
        this.stopTypingSound();
    }
    
    // Wake word detection methods
    async initializeWakeWordDetection() {
        // Remove the early return check to allow initialization

        try {
            // Initialize WebSocket connection
            await this.connectWebSocket();

            // Get microphone access for wake word detection
            // Use the existing stream if available, otherwise get new access
            if (!this.stream) {
                this.stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        sampleRate: 16000 // openWakeWord expects 16kHz audio
                    }
                });
            }

            // Initialize audio context for processing
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000 // openWakeWord expects 16kHz audio
            });

            // Resume audio context if it's suspended (Chrome autoplay policy)
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }

            // Create audio processing pipeline
            await this.setupAudioProcessing();

        } catch (error) {
            console.error('Error initializing wake word detection:', error);
            this.showError('Wake word detection unavailable');
        }
    }
    
    connectWebSocket() {
        return new Promise((resolve, reject) => {
            const wsUrl = 'ws://localhost:8000/ws/wakeword';
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected for wake word detection');
                this.updateWakeWordStatus('Listening for "Scarlett"...');
                resolve();
            };
            
            this.websocket.onmessage = (event) => {
                const message = JSON.parse(event.data);

                if (message.type === 'wakeword_detected') {
                    console.log('Primary wake word detected!', message);
                    this.handleWakeWordDetected();
                } else if (message.type === 'execution_keyword_detected') {
                    console.log('Execution keyword detected!', message);
                    this.handleExecutionKeywordDetected();
                }
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.updateWakeWordStatus('Wake word detection offline');
                
                // Attempt to reconnect after 3 seconds
                setTimeout(() => {
                    if (this.wakeWordEnabled) {
                        this.connectWebSocket();
                    }
                }, 3000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                reject(error);
            };
            
            // Keep connection alive with periodic pings
            setInterval(() => {
                if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                    this.websocket.send(JSON.stringify({ type: 'ping' }));
                }
            }, 30000); // Ping every 30 seconds
        });
    }
    
    async setupAudioProcessing() {
        // Create source from microphone stream
        const source = this.audioContext.createMediaStreamSource(this.stream);
        
        // Create script processor for capturing audio chunks
        // Buffer size of 512 samples at 16kHz = 32ms chunks
        this.scriptProcessor = this.audioContext.createScriptProcessor(512, 1, 1);
        
        this.scriptProcessor.onaudioprocess = (event) => {
            if (!this.wakeWordEnabled || !this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                return;
            }
            
            // Get audio data
            const inputData = event.inputBuffer.getChannelData(0);
            
            // Convert float32 to int16
            const int16Data = new Int16Array(inputData.length);
            for (let i = 0; i < inputData.length; i++) {
                const s = Math.max(-1, Math.min(1, inputData[i]));
                int16Data[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
            }
            
            // Convert to base64 and send
            const base64Data = btoa(String.fromCharCode.apply(null, new Uint8Array(int16Data.buffer)));
            
            this.websocket.send(JSON.stringify({
                type: 'audio',
                data: base64Data
            }));
        };
        
        // Connect audio nodes
        source.connect(this.scriptProcessor);
        this.scriptProcessor.connect(this.audioContext.destination);
    }
    
    handleWakeWordDetected() {
        if (this.isListeningAfterWakeWord) 
            return
        console.log('Wake word detected! Starting continuous listening...');

        // Flash UI to indicate detection
        this.updateWakeWordStatus('Wake word detected! Please speak... (say "skynet" to finish)');

        // Play tune to indicate processing
        const audio = new Audio('tune.wav');
        audio.volume = 1.0;
        audio.play();

        // Stop tune after 1 second with fade out
        setTimeout(() => {
            const fadeInterval = setInterval(() => {
                if (audio.volume > 0.1) {
                    audio.volume -= 0.1;
                } else {
                    clearInterval(fadeInterval);
                    audio.pause();
                    audio.currentTime = 0;
                    audio.volume = 1.0;
                }
            }, 25);
        }, 250);

        // Initialize wake word buffer with the ENTIRE main buffer
        // This captures the full 60 seconds of previous audio
        this.wakeWordAudioBuffer = this.rawAudioBuffer.map(chunk => ({
            data: new Float32Array(chunk.data), // Create a copy
            timestamp: chunk.timestamp
        }));

        console.log(`Initialized wake word buffer with ${this.wakeWordAudioBuffer.length} chunks from full buffer (up to 60 seconds)`);

        // Start listening for speech after wake word
        this.isListeningAfterWakeWord = true;
        this.silenceStartTime = null;

        // Notify backend about listening state change for execution keyword
        this.updateBackendListeningState(true);
    }

    handleExecutionKeywordDetected() {
        if (!this.isListeningAfterWakeWord) {
            return; // Should not happen, but safety check
        }

        console.log('Execution keyword "skynet" detected! Triggering end of speech processing...');

        // Update status to show execution keyword was detected
        this.updateWakeWordStatus('Execution keyword detected! Processing...');

        // Immediately trigger the same processing as silence detection
        this.handleSilenceDetected();
    }

    updateBackendListeningState(isListening) {
        // Notify backend about listening state for execution keyword detection
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'listening_state',
                is_listening: isListening
            }));
        }
    }

    updateWakeWordStatus(message) {
        // Update the wake word indicator
        this.wakeWordStatus.textContent = message;
        
        // Update indicator classes based on status
        if (message.includes('offline')) {
            this.wakeWordIndicator.classList.add('offline');
            this.wakeWordIndicator.classList.remove('active');
        } else if (message.includes('detected')) {
            this.wakeWordIndicator.classList.add('active');
            this.wakeWordIndicator.classList.remove('offline');
        } else {
            this.wakeWordIndicator.classList.remove('offline', 'active');
        }
    }
    
    async toggleWakeWordListening() {
        if (!this.wakeWordEnabled) {
            // Enable wake word listening
            this.wakeWordEnabled = true;
            this.wakeWordBtn.textContent = 'Stop Listening';
            this.wakeWordBtn.classList.add('active');
            
            try {
                // Show the wake word indicator
                this.wakeWordIndicator.style.display = 'flex';
                
                // Initialize wake word detection
                await this.initializeWakeWordDetection();
                
            } catch (error) {
                console.error('Error starting wake word detection:', error);
                this.showError('Failed to start wake word detection');
                // Revert state on error
                this.wakeWordEnabled = false;
                this.wakeWordBtn.textContent = 'Start Listening';
                this.wakeWordBtn.classList.remove('active');
                this.wakeWordIndicator.style.display = 'none';
            }
        } else {
            // Disable wake word listening
            this.wakeWordEnabled = false;
            this.wakeWordBtn.textContent = 'Start Listening';
            this.wakeWordBtn.classList.remove('active');
            
            // Hide the wake word indicator
            this.wakeWordIndicator.style.display = 'none';
            
            // Clean up resources
            if (this.websocket) {
                this.websocket.close();
                this.websocket = null;
            }
            
            if (this.scriptProcessor) {
                this.scriptProcessor.disconnect();
                this.scriptProcessor = null;
            }
            
            if (this.audioContext && this.audioContext.state !== 'closed') {
                this.audioContext.close();
                this.audioContext = null;
            }
        }
    }

    // Clean up method for when the page is unloaded
    cleanup() {
        this.isRecording = false;

        // Stop typing sound during cleanup
        this.stopTypingSound();

        if (this.bufferProcessor) {
            this.bufferProcessor.disconnect();
        }

        if (this.bufferAudioContext && this.bufferAudioContext.state !== 'closed') {
            this.bufferAudioContext.close();
        }

        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
        }

        if (this.websocket) {
            this.websocket.close();
        }

        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
        }
    }
}

// Initialize the recorder when DOM is loaded
let recorder;
document.addEventListener('DOMContentLoaded', () => {
    recorder = new AudioRecorder();
});

// Clean up when page is unloaded
window.addEventListener('beforeunload', () => {
    if (recorder) {
        recorder.cleanup();
    }
});

// Clean up when page is unloaded
window.addEventListener('beforeunload', () => {
    if (recorder) {
        recorder.cleanup();
    }
});
