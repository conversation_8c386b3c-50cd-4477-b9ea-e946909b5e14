import os
import base64
import asyncio
import json
import numpy as np
import websocket
import threading
import time
import wave
import io
from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from openai import OpenAI
from dotenv import load_dotenv
import openwakeword
from openwakeword.model import Model

# Import web search tool
from tools import get_web_search_tool_schema, process_tool_call
# Import Jellyfin tools
from jellyfin_tools import get_all_jellyfin_tool_schemas, process_jellyfin_tool_call

load_dotenv()

class RealtimeAPIClient:
    def __init__(self, api_key):
        self.api_key = api_key
        self.ws_url = "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01"
        self.ws = None
        self.connected = False
        self.response_data = {}
        self.audio_chunks = []
        self.transcript = ""
        self.error = None
        self.response_complete = False
        self.tool_calls = []
        self.tool_call_complete = False
        self.streaming_mode = False
        self.audio_queue = []
        self.lock = threading.Lock()

    def connect(self):
        """Establish WebSocket connection to Realtime API"""
        try:
            headers = [
                f"Authorization: Bearer {self.api_key}",
                "OpenAI-Beta: realtime=v1"
            ]

            self.ws = websocket.create_connection(
                self.ws_url,
                header=headers,
                timeout=30
            )
            self.connected = True
            print("Connected to OpenAI Realtime API")
            return True

        except Exception as e:
            print(f"Failed to connect to Realtime API: {e}")
            self.error = str(e)
            return False

    def disconnect(self):
        """Close WebSocket connection"""
        if self.ws:
            try:
                self.ws.close()
            except:
                pass
        self.connected = False
        self.ws = None

    def send_event(self, event):
        """Send event to Realtime API"""
        if not self.connected or not self.ws:
            raise Exception("Not connected to Realtime API")

        try:
            self.ws.send(json.dumps(event))
        except Exception as e:
            print(f"Error sending event: {e}")
            raise e

    def receive_events(self):
        """Receive and process events from Realtime API"""
        while self.connected and self.ws:
            try:
                message = self.ws.recv()
                if not message:
                    break

                event = json.loads(message)
                self.handle_event(event)

            except websocket.WebSocketTimeoutException:
                continue
            except Exception as e:
                print(f"Error receiving events: {e}")
                self.error = str(e)
                break

    def handle_event(self, event):
        """Handle incoming events from Realtime API"""
        event_type = event.get('type')

        if event_type == 'session.created':
            print("Session created")

        elif event_type == 'response.audio.delta':
            # Collect audio chunks
            with self.lock:
                audio_data = event.get('delta', '')
                if audio_data:
                    self.audio_chunks.append(audio_data)
                    # In streaming mode, also add to queue for real-time playback
                    if self.streaming_mode:
                        self.audio_queue.append(audio_data)

        elif event_type == 'response.audio.done':
            print("Audio response complete")

        elif event_type == 'response.audio_transcript.delta':
            # Collect assistant transcript chunks
            with self.lock:
                transcript_delta = event.get('delta', '')
                if transcript_delta:
                    if not hasattr(self, 'assistant_transcript'):
                        self.assistant_transcript = ''
                    self.assistant_transcript += transcript_delta

        elif event_type == 'response.audio_transcript.done':
            print("Assistant transcript complete")

        elif event_type == 'conversation.item.input_audio_transcription.completed':
            # Get user transcript
            with self.lock:
                self.transcript = event.get('transcript', '')
                print(f"User transcript: {self.transcript}")

        elif event_type == 'response.function_call_arguments.delta':
            # Handle function call arguments (streaming)
            print(f"Function call arguments delta: {event}")

        elif event_type == 'response.function_call_arguments.done':
            # Function call arguments complete
            print(f"Function call arguments complete: {event}")
            with self.lock:
                # Extract function call details
                call_id = event.get('call_id')
                name = event.get('name')
                arguments = event.get('arguments')

                if name and arguments:
                    self.tool_calls.append({
                        'id': call_id,
                        'type': 'function',
                        'function': {
                            'name': name,
                            'arguments': arguments
                        }
                    })
                    print(f"Added tool call: {name}")

        elif event_type == 'response.output_item.done':
            # Check if this is a function call item
            item = event.get('item', {})
            if item.get('type') == 'function_call':
                with self.lock:
                    self.tool_call_complete = True
                print("Function call item complete")

        elif event_type == 'response.done':
            with self.lock:
                self.response_complete = True
            print("Response complete")

        elif event_type == 'error':
            error_details = event.get('error', {})
            error_msg = error_details.get('message', 'Unknown error')
            print(f"Realtime API error: {error_msg}")
            with self.lock:
                self.error = error_msg

    def configure_session(self, tools):
        """Configure the Realtime API session"""
        # Convert tools to Realtime API format
        realtime_tools = []
        for tool in tools:
            if tool.get("type") == "function":
                realtime_tools.append({
                    "type": "function",
                    "name": tool["function"]["name"],
                    "description": tool["function"]["description"],
                    "parameters": tool["function"]["parameters"]
                })

        session_config = {
            "type": "session.update",
            "session": {
                "modalities": ["text", "audio"],
                "instructions": "You are a Scarlett, a helpful assistant. Always respond in English. You have access to web search capabilities and TV control through Jellyfin. You can play TV shows, control playback (pause, play, next, previous), and turn off the TV. Unless you are 100 percent sure you know all there is to know about something, make relevant web search before answering the user. Avoid ending your response with 'I hope that helps!' or any other generic closing remarks, including asking if the user has for any follow-up questions or tasks. Avoid giving your opinion. Keep it brief. Stick to facts. Avoid pleasantries.",
                "voice": "echo",
                "input_audio_format": "pcm16",
                "output_audio_format": "pcm16",
                "input_audio_transcription": {
                    "model": "whisper-1"
                },
                "turn_detection": None,  # Manual control
                "tools": realtime_tools,
                "tool_choice": "auto",
                "temperature": 0.8
            }
        }

        self.send_event(session_config)

    async def process_tool_calls(self):
        """Process any tool calls that were made during the response"""
        if not self.tool_calls:
            return False

        tv_control_used = False
        all_tv_control_successful = True

        for tool_call in self.tool_calls:
            function_name = tool_call['function']['name']
            print(f"Processing tool call: {function_name}")

            # Create a mock tool_call object for compatibility
            class MockToolCall:
                def __init__(self, tool_call_data):
                    self.id = tool_call_data['id']
                    self.type = tool_call_data['type']
                    self.function = type('obj', (object,), {
                        'name': tool_call_data['function']['name'],
                        'arguments': tool_call_data['function']['arguments']
                    })()

            mock_tool_call = MockToolCall(tool_call)

            try:
                if function_name == "web_search":
                    tool_response = await process_tool_call(mock_tool_call)
                elif function_name in ["jellyfin_play_show", "jellyfin_remote_control"]:
                    tool_response = await process_jellyfin_tool_call(mock_tool_call)

                    # Track TV control usage and success
                    tv_control_used = True
                    try:
                        tool_content = json.loads(tool_response["content"])
                        operation_success = tool_content.get("success", False)
                        print(f"TV control operation: {function_name}, success: {operation_success}")
                        if not operation_success:
                            all_tv_control_successful = False
                    except (json.JSONDecodeError, KeyError):
                        all_tv_control_successful = False
                        print(f"Failed to parse TV control response for {function_name}")
                else:
                    tool_response = {
                        "role": "tool",
                        "content": json.dumps({"error": f"Unknown tool: {function_name}"}),
                        "tool_call_id": tool_call['id']
                    }

                # Send tool response back to conversation
                conversation_item = {
                    "type": "conversation.item.create",
                    "item": {
                        "type": "function_call_output",
                        "call_id": tool_call['id'],
                        "output": tool_response["content"]
                    }
                }
                self.send_event(conversation_item)

            except Exception as e:
                print(f"Error processing tool call {function_name}: {e}")
                # Send error response
                error_item = {
                    "type": "conversation.item.create",
                    "item": {
                        "type": "function_call_output",
                        "call_id": tool_call['id'],
                        "output": json.dumps({"error": str(e)})
                    }
                }
                self.send_event(error_item)

        # Return whether this was a successful TV control operation
        return tv_control_used and all_tv_control_successful

    def convert_wav_to_pcm16(self, wav_data):
        """Convert WAV audio data to PCM16 format for Realtime API"""
        # Read WAV file from bytes
        wav_io = io.BytesIO(wav_data)
        with wave.open(wav_io, 'rb') as wav_file:
            # Get audio parameters
            frames = wav_file.readframes(wav_file.getnframes())
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()

            # Convert to numpy array
            if sample_width == 2:  # 16-bit
                audio_array = np.frombuffer(frames, dtype=np.int16)
            elif sample_width == 4:  # 32-bit
                audio_array = np.frombuffer(frames, dtype=np.int32)
                # Convert to 16-bit
                audio_array = (audio_array / 65536).astype(np.int16)
            else:
                raise Exception(f"Unsupported sample width: {sample_width}")

            # Convert to mono if stereo
            if channels == 2:
                audio_array = audio_array.reshape(-1, 2).mean(axis=1).astype(np.int16)

            # Resample to 24kHz if needed (Realtime API expects 24kHz)
            if sample_rate != 24000:
                # Simple resampling - for production, use proper resampling
                target_length = int(len(audio_array) * 24000 / sample_rate)
                audio_array = np.interp(
                    np.linspace(0, len(audio_array), target_length),
                    np.arange(len(audio_array)),
                    audio_array
                ).astype(np.int16)

            return audio_array.tobytes()

    def process_audio_batch(self, audio_data):
        """Process audio data in batch mode (like current system)"""
        # Reset state for new request
        with self.lock:
            self.audio_chunks = []
            self.transcript = ""
            self.assistant_transcript = ""
            self.error = None
            self.response_complete = False
            self.tool_calls = []
            self.tool_call_complete = False

        try:
            # Convert WAV to PCM16 format
            pcm16_data = self.convert_wav_to_pcm16(audio_data)

            # Encode as base64
            audio_base64 = base64.b64encode(pcm16_data).decode('utf-8')

            # Send audio to buffer in chunks (max 15MB per chunk)
            chunk_size = 15 * 1024 * 1024  # 15MB in base64 is roughly 11MB in binary
            audio_bytes = base64.b64decode(audio_base64)

            for i in range(0, len(audio_bytes), chunk_size):
                chunk = audio_bytes[i:i + chunk_size]
                chunk_base64 = base64.b64encode(chunk).decode('utf-8')

                append_event = {
                    "type": "input_audio_buffer.append",
                    "audio": chunk_base64
                }
                self.send_event(append_event)

            # Commit the audio buffer
            commit_event = {
                "type": "input_audio_buffer.commit"
            }
            self.send_event(commit_event)

            # Create response
            response_event = {
                "type": "response.create",
                "response": {
                    "modalities": ["text", "audio"],
                    "instructions": None  # Use session instructions
                }
            }
            self.send_event(response_event)

            # Start receiving events in background thread
            receive_thread = threading.Thread(target=self.receive_events)
            receive_thread.daemon = True
            receive_thread.start()

            # Wait for initial response to complete (with timeout)
            timeout = 30  # 30 seconds timeout
            start_time = time.time()

            while not self.response_complete and not self.error:
                if time.time() - start_time > timeout:
                    raise Exception("Response timeout")
                time.sleep(0.1)

            if self.error:
                raise Exception(f"Realtime API error: {self.error}")

            # Check if we have tool calls to process
            if self.tool_calls:
                print(f"Processing {len(self.tool_calls)} tool calls")

                # Process tool calls synchronously
                import asyncio
                is_silent_tv_control = asyncio.run(self.process_tool_calls())

                if is_silent_tv_control:
                    print("TV control operation successful - returning silent response")
                    return {
                        'silent_response': True,
                        'message': 'TV control operation completed successfully',
                        'user_transcript': self.transcript
                    }

                # Reset response state for final response
                with self.lock:
                    self.audio_chunks = []
                    self.assistant_transcript = ""
                    self.response_complete = False

                # Create final response after tool calls
                final_response_event = {
                    "type": "response.create",
                    "response": {
                        "modalities": ["text", "audio"],
                        "instructions": None
                    }
                }
                self.send_event(final_response_event)

                # Wait for final response
                start_time = time.time()
                while not self.response_complete and not self.error:
                    if time.time() - start_time > timeout:
                        raise Exception("Final response timeout")
                    time.sleep(0.1)

                if self.error:
                    raise Exception(f"Realtime API error in final response: {self.error}")

            # Combine audio chunks and convert back to MP3 format
            with self.lock:
                if self.audio_chunks:
                    # Combine PCM16 chunks
                    combined_pcm = b''.join([base64.b64decode(chunk) for chunk in self.audio_chunks])

                    # Convert PCM16 back to base64 MP3 format for compatibility
                    # For now, just return the base64 encoded PCM data
                    # TODO: Convert to actual MP3 format
                    combined_audio_b64 = base64.b64encode(combined_pcm).decode('utf-8')

                    return {
                        'audio_data': combined_audio_b64,
                        'transcript': getattr(self, 'assistant_transcript', ''),
                        'user_transcript': self.transcript
                    }
                else:
                    raise Exception("No audio response received")

        except Exception as e:
            print(f"Error processing audio batch: {e}")
            raise e

    def start_streaming_session(self):
        """Start a streaming session for real-time audio processing"""
        self.streaming_mode = True

        # Reset state
        with self.lock:
            self.audio_chunks = []
            self.transcript = ""
            self.assistant_transcript = ""
            self.error = None
            self.response_complete = False
            self.tool_calls = []
            self.tool_call_complete = False
            self.audio_queue = []

        # Start receiving events in background thread
        receive_thread = threading.Thread(target=self.receive_events)
        receive_thread.daemon = True
        receive_thread.start()

        print("Streaming session started")

    def stream_audio_chunk(self, audio_data):
        """Stream a chunk of audio data to the Realtime API"""
        if not self.streaming_mode:
            raise Exception("Not in streaming mode")

        try:
            # Convert WAV chunk to PCM16 format
            pcm16_data = self.convert_wav_to_pcm16(audio_data)

            # Encode as base64
            audio_base64 = base64.b64encode(pcm16_data).decode('utf-8')

            # Send audio chunk to buffer
            append_event = {
                "type": "input_audio_buffer.append",
                "audio": audio_base64
            }
            self.send_event(append_event)

        except Exception as e:
            print(f"Error streaming audio chunk: {e}")
            raise e

    def commit_audio_and_respond(self):
        """Commit the current audio buffer and request a response"""
        if not self.streaming_mode:
            raise Exception("Not in streaming mode")

        try:
            # Commit the audio buffer
            commit_event = {
                "type": "input_audio_buffer.commit"
            }
            self.send_event(commit_event)

            # Create response
            response_event = {
                "type": "response.create",
                "response": {
                    "modalities": ["text", "audio"],
                    "instructions": None
                }
            }
            self.send_event(response_event)

        except Exception as e:
            print(f"Error committing audio: {e}")
            raise e

    def get_audio_chunk(self):
        """Get the next audio chunk from the response queue"""
        with self.lock:
            if self.audio_queue:
                return self.audio_queue.pop(0)
            return None

    def stop_streaming_session(self):
        """Stop the streaming session"""
        self.streaming_mode = False
        print("Streaming session stopped")

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Initialize Realtime API client
realtime_client = None

def get_realtime_client():
    """Get or create Realtime API client"""
    global realtime_client
    if realtime_client is None:
        realtime_client = RealtimeAPIClient(os.getenv("OPENAI_API_KEY"))
    return realtime_client

# Initialize wake word models
print("Loading wake word models...")
wake_word_model = Model(
    wakeword_models=["VoiceModels/scarlett.onnx", "VoiceModels/skynet.onnx"],
    inference_framework="onnx"  # Use ONNX for better cross-platform support
)
print("Wake word models initialized (Scarlett + Skynet execution keyword)")

# Define available tools
AVAILABLE_TOOLS = [
    get_web_search_tool_schema()
] + get_all_jellyfin_tool_schemas()

@app.post("/api/chat")
async def chat_with_audio(audio_file: UploadFile = File(...)):
    try:
        audio_data = await audio_file.read()

        if len(audio_data) > 20 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="Audio file too large (max 20MB)")

        # Get Realtime API client
        rt_client = get_realtime_client()

        # Connect if not already connected
        if not rt_client.connected:
            if not rt_client.connect():
                raise HTTPException(status_code=500, detail="Failed to connect to Realtime API")

            # Configure session with tools
            rt_client.configure_session(AVAILABLE_TOOLS)

        # Process audio using Realtime API
        loop = asyncio.get_event_loop()
        response_data = await loop.run_in_executor(
            None,
            lambda: rt_client.process_audio_batch(audio_data)
        )

        # Check if this is a silent TV control response
        if response_data.get('silent_response'):
            print("Returning silent response for successful TV control")
            return JSONResponse({
                "silent_response": True,
                "message": response_data['message'],
                "user_transcript": response_data['user_transcript']
            })

        return JSONResponse({
            "audio_data": response_data['audio_data'],
            "transcript": response_data['transcript'],
            "user_transcript": response_data['user_transcript']
        })

    except Exception as e:
        print(f"Error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "ok"}

@app.websocket("/ws/realtime")
async def websocket_realtime(websocket: WebSocket):
    await websocket.accept()
    print("WebSocket connection established for realtime audio")

    rt_client = None

    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)

            if message["type"] == "start_session":
                # Initialize Realtime API client
                rt_client = get_realtime_client()

                if not rt_client.connected:
                    if not rt_client.connect():
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "message": "Failed to connect to Realtime API"
                        }))
                        continue

                    # Configure session with tools
                    rt_client.configure_session(AVAILABLE_TOOLS)

                # Start streaming session
                rt_client.start_streaming_session()

                await websocket.send_text(json.dumps({
                    "type": "session_started"
                }))

                # Start background task to send audio chunks
                asyncio.create_task(stream_audio_to_client(websocket, rt_client))

            elif message["type"] == "audio_chunk":
                if rt_client and rt_client.streaming_mode:
                    # Decode base64 audio data
                    audio_data = base64.b64decode(message["data"])

                    # Stream to Realtime API
                    try:
                        rt_client.stream_audio_chunk(audio_data)
                    except Exception as e:
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "message": f"Error streaming audio: {str(e)}"
                        }))

            elif message["type"] == "commit_audio":
                if rt_client and rt_client.streaming_mode:
                    try:
                        rt_client.commit_audio_and_respond()
                    except Exception as e:
                        await websocket.send_text(json.dumps({
                            "type": "error",
                            "message": f"Error committing audio: {str(e)}"
                        }))

            elif message["type"] == "stop_session":
                if rt_client:
                    rt_client.stop_streaming_session()
                await websocket.send_text(json.dumps({
                    "type": "session_stopped"
                }))

    except WebSocketDisconnect:
        print("Realtime WebSocket disconnected")
        if rt_client:
            rt_client.stop_streaming_session()
    except Exception as e:
        print(f"Realtime WebSocket error: {e}")
        if rt_client:
            rt_client.stop_streaming_session()
        await websocket.close()

async def stream_audio_to_client(websocket: WebSocket, rt_client):
    """Background task to stream audio chunks to client"""
    try:
        while rt_client.streaming_mode:
            # Check for new audio chunks
            audio_chunk = rt_client.get_audio_chunk()
            if audio_chunk:
                await websocket.send_text(json.dumps({
                    "type": "audio_chunk",
                    "data": audio_chunk
                }))

            # Small delay to prevent busy waiting
            await asyncio.sleep(0.01)  # 10ms

    except Exception as e:
        print(f"Error streaming audio to client: {e}")

@app.websocket("/ws/wakeword")
async def websocket_wakeword(websocket: WebSocket):
    await websocket.accept()
    print("WebSocket connection established for wake word detection")

    # Track listening state for execution keyword
    is_listening_after_wakeword = False

    try:
        while True:
            # Receive audio data from the client
            data = await websocket.receive_text()
            message = json.loads(data)

            if message["type"] == "audio":
                # Convert base64 audio to numpy array
                audio_bytes = base64.b64decode(message["data"])
                audio_array = np.frombuffer(audio_bytes, dtype=np.int16)

                # Ensure audio is mono and 16kHz for openWakeWord
                # The frontend will send 16kHz mono audio

                # Run wake word detection
                prediction = wake_word_model.predict(audio_array)

                # Check if wake word was detected
                # prediction is a dict with model names as keys and scores as values
                for model_name, score in prediction.items():
                    if score > 0.5:  # Threshold for detection
                        # Handle Scarlett (primary wake word) - always active
                        if "scarlett" in model_name.lower():
                            print(f"Primary wake word detected: {model_name} (score: {score})")
                            is_listening_after_wakeword = True
                            await websocket.send_text(json.dumps({
                                "type": "wakeword_detected",
                                "model": model_name,
                                "score": float(score)
                            }))
                            break

                        # Handle Skynet (execution keyword) - only when actively listening
                        elif "skynet" in model_name.lower() and is_listening_after_wakeword:
                            print(f"Execution keyword detected: {model_name} (score: {score})")
                            is_listening_after_wakeword = False  # Reset state
                            await websocket.send_text(json.dumps({
                                "type": "execution_keyword_detected",
                                "model": model_name,
                                "score": float(score)
                            }))
                            break

            elif message["type"] == "ping":
                # Keep connection alive
                await websocket.send_text(json.dumps({"type": "pong"}))

            elif message["type"] == "listening_state":
                # Update listening state from frontend
                is_listening_after_wakeword = message.get("is_listening", False)

    except WebSocketDisconnect:
        print("WebSocket disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")
        await websocket.close()

# Mount the frontend directory AFTER defining all API and WebSocket routes
frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "frontend")
if os.path.exists(frontend_path):
    app.mount("/", StaticFiles(directory=frontend_path, html=True), name="frontend")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)